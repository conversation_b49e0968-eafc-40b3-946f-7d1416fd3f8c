{{-- Advanced Search & Filters Component --}}
{{-- Premium bank-style filtering with responsive design --}}

<div class="bank-card mb-6" x-data="branchFilters()" x-init="init()">
    <div class="bank-card-header">
        <div>
            <h3 class="bank-card-title">Search & Filters</h3>
            <p class="bank-card-subtitle">Advanced search and filtering options</p>
        </div>
        <div class="flex items-center space-x-2">
            <button @click="toggleFilters()" class="btn-bank btn-bank-outline btn-bank-sm">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z">
                    </path>
                </svg>
                <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'"></span>
            </button>
            <button @click="clearAllFilters()"
                class="btn-bank btn-bank-outline btn-bank-sm text-error-red border-error-red hover:bg-error-red hover:text-white">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
                Clear All
            </button>
        </div>
    </div>

    <div class="bank-card-body">
        <!-- Quick Search -->
        <div class="mb-6">
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="w-5 h-5 text-dark-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input type="text" name="search" value="{{ request('search') }}"
                    placeholder="Search branches by name, location, address, phone, or email..."
                    class="form-input-bank pl-10 w-full" x-model="filters.search"
                    @input.debounce.500ms="applyFilters()">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <div x-show="filters.search" @click="filters.search = ''; applyFilters()"
                        class="cursor-pointer text-dark-gray hover:text-charcoal-black">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showFilters" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform -translate-y-2"
            x-transition:enter-end="opacity-100 transform translate-y-0"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform translate-y-0"
            x-transition:leave-end="opacity-0 transform -translate-y-2">

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <!-- Status Filter -->
                <div>
                    <label class="form-label-bank">Status</label>
                    <select name="status" class="form-select-bank" x-model="filters.status" @change="applyFilters()">
                        <option value="">All Statuses</option>
                        <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Active</option>
                        <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>

                <!-- Location Filter -->
                <div>
                    <label class="form-label-bank">Location</label>
                    <select name="location" class="form-select-bank" x-model="filters.location"
                        @change="applyFilters()">
                        <option value="">All Locations</option>
                        @foreach ($availableLocations ?? [] as $location)
                            <option value="{{ $location }}"
                                {{ request('location') == $location ? 'selected' : '' }}>
                                {{ $location }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Date Range From -->
                <div>
                    <label class="form-label-bank">Created From</label>
                    <input type="date" name="date_from" value="{{ request('date_from') }}" class="form-input-bank"
                        x-model="filters.date_from" @change="applyFilters()">
                </div>

                <!-- Date Range To -->
                <div>
                    <label class="form-label-bank">Created To</label>
                    <input type="date" name="date_to" value="{{ request('date_to') }}" class="form-input-bank"
                        x-model="filters.date_to" @change="applyFilters()">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <!-- Student Count Range -->
                <div>
                    <label class="form-label-bank">Min Students</label>
                    <input type="number" name="min_students" value="{{ request('min_students') }}" placeholder="0"
                        min="0" class="form-input-bank" x-model="filters.min_students"
                        @input.debounce.500ms="applyFilters()">
                </div>

                <div>
                    <label class="form-label-bank">Max Students</label>
                    <input type="number" name="max_students" value="{{ request('max_students') }}"
                        placeholder="1000" min="0" class="form-input-bank" x-model="filters.max_students"
                        @input.debounce.500ms="applyFilters()">
                </div>

                <!-- Academy Count Range -->
                <div>
                    <label class="form-label-bank">Min Academies</label>
                    <input type="number" name="min_academies" value="{{ request('min_academies') }}"
                        placeholder="0" min="0" class="form-input-bank" x-model="filters.min_academies"
                        @input.debounce.500ms="applyFilters()">
                </div>

                <div>
                    <label class="form-label-bank">Max Academies</label>
                    <input type="number" name="max_academies" value="{{ request('max_academies') }}"
                        placeholder="50" min="0" class="form-input-bank" x-model="filters.max_academies"
                        @input.debounce.500ms="applyFilters()">
                </div>
            </div>

            <!-- Sort Options -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label class="form-label-bank">Sort By</label>
                    <select name="sort_by" class="form-select-bank" x-model="filters.sort_by"
                        @change="applyFilters()">
                        <option value="name" {{ request('sort_by', 'name') == 'name' ? 'selected' : '' }}>Name
                        </option>
                        <option value="location" {{ request('sort_by') == 'location' ? 'selected' : '' }}>Location
                        </option>
                        <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>Created
                            Date</option>
                        <option value="students_count" {{ request('sort_by') == 'students_count' ? 'selected' : '' }}>
                            Student Count</option>
                        <option value="academies_count"
                            {{ request('sort_by') == 'academies_count' ? 'selected' : '' }}>Academy Count</option>
                    </select>
                </div>

                <div>
                    <label class="form-label-bank">Sort Direction</label>
                    <select name="sort_direction" class="form-select-bank" x-model="filters.sort_direction"
                        @change="applyFilters()">
                        <option value="asc" {{ request('sort_direction', 'asc') == 'asc' ? 'selected' : '' }}>
                            Ascending</option>
                        <option value="desc" {{ request('sort_direction') == 'desc' ? 'selected' : '' }}>Descending
                        </option>
                    </select>
                </div>
            </div>

            <!-- Items Per Page -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="form-label-bank">Items Per Page</label>
                    <select name="per_page" class="form-select-bank" x-model="filters.per_page"
                        @change="applyFilters()">
                        <option value="10" {{ request('per_page', 15) == 10 ? 'selected' : '' }}>10</option>
                        <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ request('per_page', 15) == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ request('per_page', 15) == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ request('per_page', 15) == 100 ? 'selected' : '' }}>100</option>
                    </select>
                </div>

                <div class="flex items-end">
                    <button @click="applyFilters()" class="btn-bank w-full">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Apply Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Active Filters Display -->
        <div x-show="hasActiveFilters()" x-transition class="mt-4 pt-4 border-t border-light-gray">
            <div class="flex items-center justify-between mb-3">
                <h4 class="text-sm font-semibold text-charcoal-black">Active Filters:</h4>
                <button @click="clearAllFilters()" class="text-sm text-error-red hover:text-error-red-dark">
                    Clear All
                </button>
            </div>
            <div class="flex flex-wrap gap-2">
                <template x-for="filter in getActiveFilters()" :key="filter.key">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-leaders-red text-white">
                        <span x-text="filter.label"></span>
                        <button @click="removeFilter(filter.key)" class="ml-2 hover:text-gray-200">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </span>
                </template>
            </div>
        </div>
    </div>
</div>

<script>
    function branchFilters() {
        return {
            showFilters: localStorage.getItem('branchFiltersExpanded') === 'true',
            filters: {
                search: '{{ request('search') }}',
                status: '{{ request('status') }}',
                location: '{{ request('location') }}',
                date_from: '{{ request('date_from') }}',
                date_to: '{{ request('date_to') }}',
                min_students: '{{ request('min_students') }}',
                max_students: '{{ request('max_students') }}',
                min_academies: '{{ request('min_academies') }}',
                max_academies: '{{ request('max_academies') }}',
                sort_by: '{{ request('sort_by', 'name') }}',
                sort_direction: '{{ request('sort_direction', 'asc') }}',
                per_page: '{{ request('per_page', 15) }}'
            },

            init() {
                // Auto-expand filters if any are active
                if (this.hasActiveFilters()) {
                    this.showFilters = true;
                }
            },

            toggleFilters() {
                this.showFilters = !this.showFilters;
                localStorage.setItem('branchFiltersExpanded', this.showFilters);
            },

            applyFilters() {
                const params = new URLSearchParams();

                Object.keys(this.filters).forEach(key => {
                    if (this.filters[key] && this.filters[key] !== '') {
                        params.append(key, this.filters[key]);
                    }
                });

                const url = new URL(window.location);
                url.search = params.toString();
                window.location.href = url.toString();
            },

            clearAllFilters() {
                Object.keys(this.filters).forEach(key => {
                    if (key !== 'sort_by' && key !== 'sort_direction' && key !== 'per_page') {
                        this.filters[key] = '';
                    }
                });
                this.applyFilters();
            },

            hasActiveFilters() {
                return Object.keys(this.filters).some(key => {
                    if (key === 'sort_by' && this.filters[key] === 'name') return false;
                    if (key === 'sort_direction' && this.filters[key] === 'asc') return false;
                    if (key === 'per_page' && this.filters[key] === '15') return false;
                    return this.filters[key] && this.filters[key] !== '';
                });
            },

            getActiveFilters() {
                const activeFilters = [];
                const labels = {
                    search: 'Search',
                    status: 'Status',
                    location: 'Location',
                    date_from: 'From Date',
                    date_to: 'To Date',
                    min_students: 'Min Students',
                    max_students: 'Max Students',
                    min_academies: 'Min Academies',
                    max_academies: 'Max Academies',
                    sort_by: 'Sort By',
                    sort_direction: 'Sort Direction',
                    per_page: 'Per Page'
                };

                Object.keys(this.filters).forEach(key => {
                    if (key === 'sort_by' && this.filters[key] === 'name') return;
                    if (key === 'sort_direction' && this.filters[key] === 'asc') return;
                    if (key === 'per_page' && this.filters[key] === '15') return;

                    if (this.filters[key] && this.filters[key] !== '') {
                        let value = this.filters[key];
                        if (key === 'status') {
                            value = value === '1' ? 'Active' : 'Inactive';
                        }
                        activeFilters.push({
                            key: key,
                            label: `${labels[key]}: ${value}`
                        });
                    }
                });

                return activeFilters;
            },

            removeFilter(key) {
                if (key === 'sort_by') {
                    this.filters[key] = 'name';
                } else if (key === 'sort_direction') {
                    this.filters[key] = 'asc';
                } else if (key === 'per_page') {
                    this.filters[key] = '15';
                } else {
                    this.filters[key] = '';
                }
                this.applyFilters();
            }
        }
    }
</script>
