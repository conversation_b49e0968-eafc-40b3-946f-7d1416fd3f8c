{{-- Branch Management Statistics Cards --}}
{{-- Premium bank-style statistics with real-time updates --}}

<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
    <!-- Total Branches -->
    <div class="stats-card scale-in" style="animation-delay: 0.1s;">
        <div class="stats-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                </path>
            </svg>
        </div>
        <div class="stats-value" x-data="{ count: 0 }" x-init="animateCounter(count, {{ $filteredStats['total_branches'] ?? 0 }}, 1000)">
            <span x-text="count"></span>
        </div>
        <div class="stats-label">Total Branches</div>
        <div class="stats-change positive">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                </path>
            </svg>
            All Branches
        </div>
    </div>

    <!-- Active Branches -->
    <div class="stats-card scale-in" style="animation-delay: 0.2s;">
        <div class="stats-icon bg-gradient-to-br from-success-green to-green-600">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <div class="stats-value" x-data="{ count: 0 }" x-init="animateCounter(count, {{ $filteredStats['active_branches'] ?? 0 }}, 1000)">
            <span x-text="count"></span>
        </div>
        <div class="stats-label">Active Branches</div>
        <div class="stats-change positive">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                </path>
            </svg>
            {{ number_format((($filteredStats['active_branches'] ?? 0) / max($filteredStats['total_branches'] ?? 1, 1)) * 100, 1) }}%
            Active Rate
        </div>
    </div>

    <!-- Total Students -->
    <div class="stats-card scale-in" style="animation-delay: 0.3s;">
        <div class="stats-icon bg-gradient-to-br from-gold-yellow to-yellow-600">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                </path>
            </svg>
        </div>
        <div class="stats-value" x-data="{ count: 0 }" x-init="animateCounter(count, {{ $filteredStats['total_students'] ?? 0 }}, 1500)">
            <span x-text="count"></span>
        </div>
        <div class="stats-label">Total Students</div>
        <div class="stats-change positive">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z">
                </path>
            </svg>
            Across All Branches
        </div>
    </div>

    <!-- Monthly Revenue -->
    <div class="stats-card scale-in" style="animation-delay: 0.4s;">
        <div class="stats-icon bg-gradient-to-br from-info-blue to-blue-600">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                </path>
            </svg>
        </div>
        <div class="stats-value text-leaders-red" x-data="{ count: 0 }" x-init="animateCounter(count, {{ $filteredStats['total_revenue'] ?? 0 }}, 2000)">
            <span x-text="formatCurrency(count)"></span>
        </div>
        <div class="stats-label">Total Revenue (AED)</div>
        <div class="stats-change positive">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6">
                </path>
            </svg>
            All Branches Revenue
        </div>
    </div>
</div>

<!-- Performance Indicators -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- Branch Performance -->
    <div class="bank-card">
        <div class="bank-card-header">
            <div>
                <h4 class="bank-card-title text-lg">Branch Performance</h4>
                <p class="bank-card-subtitle">Key performance metrics</p>
            </div>
            <div
                class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                    </path>
                </svg>
            </div>
        </div>
        <div class="bank-card-body">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-dark-gray">Average Students per Branch</span>
                    <span class="font-semibold text-charcoal-black">
                        {{ number_format(($filteredStats['total_students'] ?? 0) / max($filteredStats['total_branches'] ?? 1, 1), 1) }}
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-dark-gray">Average Revenue per Branch</span>
                    <span class="font-semibold text-leaders-red">
                        {{ number_format(($filteredStats['total_revenue'] ?? 0) / max($filteredStats['total_branches'] ?? 1, 1), 0) }}
                        AED
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-dark-gray">Branch Utilization Rate</span>
                    <span class="font-semibold text-success-green">
                        {{ number_format((($filteredStats['active_branches'] ?? 0) / max($filteredStats['total_branches'] ?? 1, 1)) * 100, 1) }}%
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performing Branches -->
    <div class="bank-card">
        <div class="bank-card-header">
            <div>
                <h4 class="bank-card-title text-lg">Top Performers</h4>
                <p class="bank-card-subtitle">Highest revenue branches</p>
            </div>
            <div
                class="w-12 h-12 bg-gradient-to-br from-gold-yellow to-yellow-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z">
                    </path>
                </svg>
            </div>
        </div>
        <div class="bank-card-body">
            <div class="space-y-3">
                @forelse([] as $index => $branch)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div
                                class="w-8 h-8 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center text-white text-sm font-bold">
                                {{ $index + 1 }}
                            </div>
                            <div>
                                <div class="font-semibold text-charcoal-black">{{ $branch->name }}</div>
                                <div class="text-sm text-dark-gray">{{ $branch->location }}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-semibold text-leaders-red">
                                {{ number_format($branch->monthly_revenue ?? 0, 0) }} AED</div>
                            <div class="text-sm text-dark-gray">{{ $branch->students_count ?? 0 }} students</div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-4">
                        <p class="text-dark-gray">No performance data available</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bank-card">
        <div class="bank-card-header">
            <div>
                <h4 class="bank-card-title text-lg">Recent Activity</h4>
                <p class="bank-card-subtitle">Latest branch updates</p>
            </div>
            <div
                class="w-12 h-12 bg-gradient-to-br from-info-blue to-blue-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
        <div class="bank-card-body">
            <div class="space-y-3">
                @forelse([] as $activity)
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-leaders-red rounded-full mt-2 flex-shrink-0"></div>
                        <div class="flex-1">
                            <div class="text-sm text-charcoal-black">{{ $activity->description }}</div>
                            <div class="text-xs text-dark-gray">{{ $activity->created_at->diffForHumans() }}</div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-4">
                        <p class="text-dark-gray">No recent activity</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>

<script>
    function animateCounter(element, target, duration) {
        let start = 0;
        const increment = target / (duration / 16);
        const timer = setInterval(() => {
            start += increment;
            element.count = Math.floor(start);
            if (start >= target) {
                element.count = target;
                clearInterval(timer);
            }
        }, 16);
    }

    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }
</script>
