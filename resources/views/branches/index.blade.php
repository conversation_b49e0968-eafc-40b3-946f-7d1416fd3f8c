@extends('layouts.dashboard')

@section('title', 'Branch Management - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Branch Management</h1>
                <p class="text-lg text-dark-gray">Manage academy branches and locations</p>
                <span class="badge-bank badge-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ $branches->total() }} Total Branches
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('branches.create') }}" class="btn-bank">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                    </path>
                </svg>
                Add New Branch
            </a>
            <div class="flex items-center space-x-2">
                <button onclick="exportData('excel')" class="btn-bank btn-bank-outline">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                    Export Excel
                </button>
                <button onclick="exportData('pdf')" class="btn-bank btn-bank-outline">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                        </path>
                    </svg>
                    Export PDF
                </button>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="branchManagement()" x-init="init()">
        <!-- Statistics Cards -->
        @include('branches._stats')

        <!-- Advanced Search & Filters -->
        @include('branches._filters')

        <!-- Main Content Card -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">All Branches</h3>
                    <p class="bank-card-subtitle">
                        Showing {{ $branches->firstItem() ?? 0 }} to {{ $branches->lastItem() ?? 0 }}
                        of {{ $branches->total() }} branches
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Bulk Actions -->
                    <div x-show="selectedBranches.length > 0" x-transition class="flex items-center space-x-2">
                        <span class="text-sm text-dark-gray" x-text="`${selectedBranches.length} selected`"></span>
                        <select x-model="bulkAction" class="form-select-bank text-sm">
                            <option value="">Bulk Actions</option>
                            <option value="activate">Activate Selected</option>
                            <option value="deactivate">Deactivate Selected</option>
                            <option value="delete">Delete Selected</option>
                        </select>
                        <button @click="executeBulkAction()" :disabled="!bulkAction" class="btn-bank btn-bank-sm">
                            Apply
                        </button>
                    </div>

                    <!-- View Toggle -->
                    <div class="flex items-center bg-off-white rounded-lg p-1">
                        <button @click="viewMode = 'table'" :class="viewMode === 'table' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 10h18M3 6h18m-9 10h9"></path>
                            </svg>
                        </button>
                        <button @click="viewMode = 'grid'" :class="viewMode === 'grid' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bank-card-body p-0">
                <!-- Table View -->
                <div x-show="viewMode === 'table'" x-transition>
                    @include('branches._table')
                </div>

                <!-- Grid View -->
                <div x-show="viewMode === 'grid'" x-transition>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                            @foreach ($branches as $branch)
                                <div
                                    class="bg-white border border-medium-gray rounded-lg p-6 hover:shadow-lg transition-all duration-300">
                                    <div class="flex items-start justify-between mb-4">
                                        <div class="flex items-center space-x-3">
                                            <div
                                                class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                                    </path>
                                                </svg>
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-charcoal-black">{{ $branch->name }}</h4>
                                                <p class="text-sm text-dark-gray">{{ $branch->location }}</p>
                                            </div>
                                        </div>
                                        <span class="badge-bank {{ $branch->status ? 'badge-success' : 'badge-neutral' }}">
                                            {{ $branch->status ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>

                                    <div class="grid grid-cols-2 gap-4 mb-4">
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-leaders-red">
                                                {{ $branch->academies_count ?? 0 }}</div>
                                            <div class="text-xs text-dark-gray">Academies</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-success-green">
                                                {{ $branch->students_count ?? 0 }}</div>
                                            <div class="text-xs text-dark-gray">Students</div>
                                        </div>
                                    </div>

                                    <div class="flex items-center justify-between pt-4 border-t border-light-gray">
                                        <div class="flex items-center space-x-2">
                                            @can('view', $branch)
                                                <a href="{{ route('branches.show', $branch) }}"
                                                    class="btn-bank btn-bank-sm btn-bank-outline">
                                                    View
                                                </a>
                                            @endcan
                                            @can('update', $branch)
                                                <a href="{{ route('branches.edit', $branch) }}" class="btn-bank btn-bank-sm">
                                                    Edit
                                                </a>
                                            @endcan
                                        </div>
                                        <div class="text-xs text-dark-gray">
                                            {{ $branch->created_at->format('M d, Y') }}
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if ($branches->hasPages())
                    <div class="px-6 py-4 border-t border-light-gray">
                        {{ $branches->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function branchManagement() {
            return {
                selectedBranches: [],
                bulkAction: '',
                viewMode: localStorage.getItem('branchViewMode') || 'table',

                init() {
                    this.$watch('viewMode', (value) => {
                        localStorage.setItem('branchViewMode', value);
                    });
                },

                toggleBranchSelection(branchId) {
                    const index = this.selectedBranches.indexOf(branchId);
                    if (index > -1) {
                        this.selectedBranches.splice(index, 1);
                    } else {
                        this.selectedBranches.push(branchId);
                    }
                },

                selectAllBranches() {
                    const checkboxes = document.querySelectorAll('input[name="branch_ids[]"]');
                    const allSelected = this.selectedBranches.length === checkboxes.length;

                    if (allSelected) {
                        this.selectedBranches = [];
                    } else {
                        this.selectedBranches = Array.from(checkboxes).map(cb => parseInt(cb.value));
                    }
                },

                async executeBulkAction() {
                    if (!this.bulkAction || this.selectedBranches.length === 0) return;

                    const confirmed = await this.confirmBulkAction();
                    if (!confirmed) return;

                    try {
                        const response = await fetch('{{ route('branches.bulk-action') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                                action: this.bulkAction,
                                branch_ids: this.selectedBranches
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.reload();
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while processing the request.');
                    }
                },

                confirmBulkAction() {
                    const actionText = this.bulkAction.charAt(0).toUpperCase() + this.bulkAction.slice(1);
                    return confirm(
                        `Are you sure you want to ${actionText.toLowerCase()} ${this.selectedBranches.length} selected branch(es)?`
                    );
                }
            }
        }

        function exportData(format) {
            const url = format === 'excel' ? '{{ route('branches.export.excel') }}' :
                '{{ route('branches.export.pdf') }}';
            const params = new URLSearchParams(window.location.search);
            window.open(`${url}?${params.toString()}`, '_blank');
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            alert(message);
        }
    </script>
@endpush
